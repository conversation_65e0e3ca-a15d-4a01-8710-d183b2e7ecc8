"use client";

import { Box, Typography, Skeleton, IconButton } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import { getThumborUrl } from "../../utils/getThumborUrl";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import MedicationIcon from "@mui/icons-material/Medication";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useRef } from "react";

const Speciality = ({ specialities = [], isLoading = false, locationCode }) => {
  const router = useRouter();
  const theme = useTheme();
  const swiperRef = useRef(null);

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: "24px", overflow: "hidden" }}>
        {[1, 2, 3].map((_, index) => (
          <Skeleton
            key={index}
            variant="rounded"
            height={300}
            sx={{ 
              width: "100%", 
              minWidth: { xs: "280px", sm: "320px", md: "350px" },
              borderRadius: "12px"
            }}
          />
        ))}
      </Box>
    );
  }

  const handleSpecialityClick = (speciality) => {
    const { seoSlug = "" } = speciality || {};
    if (seoSlug && locationCode) {
      router.push(`/specialities/${locationCode}/${seoSlug}`);
    }
  };

  return (
    <Box sx={{ overflow: "hidden" }}> {/* Prevent overflow */}
      <Swiper
        ref={swiperRef}
        spaceBetween={24}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          600: {
            slidesPerView: 2,
          },
          900: {
            slidesPerView: 2.5,
          },
          1200: {
            slidesPerView: 3,
          },
        }}
        modules={[Autoplay, Navigation]}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        loop={true}
        navigation={false}
        className="specialitySwiper"
        style={{
          paddingBottom: "8px", // Small padding to prevent shadow cutoff
        }}
      >
        {specialities.map((speciality, index) => {
          const {
            code = null,
            displayName = "",
            shortDescription = "",
            iconUrl = "",
            bannerUrl = "",
            seoSlug = "",
          } = speciality || {};

          return (
            <SwiperSlide key={code || index}>
              <Box
                sx={{
                  background: "#fff",
                  borderRadius: "16px",
                  overflow: "hidden",
                  cursor: "pointer",
                  transition: "box-shadow 0.3s ease",
                  height: "300px", // Fixed height for all cards
                  display: "flex",
                  flexDirection: "column",
                  border: "1px solid rgba(0,0,0,0.08)", // Light border
                  "&:hover": {
                    boxShadow: "0 1px 8px rgba(0,0,0,0.12)", // Slightly more shadow on hover, no elevation
                  },
                }}
                onClick={() => handleSpecialityClick(speciality)}
              >
                {/* Image Section */}
                <Box
                  sx={{
                    height: "180px",
                    position: "relative",
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {bannerUrl || iconUrl ? (
                    <Image
                      alt={displayName || "speciality"}
                      src={getThumborUrl(bannerUrl , 350, 200)}
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  ) : (
                    <Box
                      sx={{
                        width: "80px",
                        height: "80px",
                        borderRadius: "50%",
                        background: theme.palette.primary.main,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <MedicationIcon 
                        sx={{ 
                          fontSize: "40px", 
                          color: "#fff" 
                        }} 
                      />
                    </Box>
                  )}
                </Box>

                {/* Content Section */}
                <Box
                  sx={{
                    padding: "18px 16px 16px 16px", // More top padding
                    height: "100px",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "flex-start", // Align to top instead of space-between
                    gap: "10px", // Consistent gap between title and description
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 500,
                      fontSize: "18px",
                      color: theme.palette.text.primary,
                      lineHeight: 1.3,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: "1",
                      WebkitBoxOrient: "vertical",
                      minHeight: "30px", // Ensure consistent height for title area
                    }}
                  >
                    {displayName || "Name not available."}
                  </Typography>

                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.6)",
                      fontSize: "14px",
                      lineHeight: 1.4,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: "2", // Limit to exactly 2 lines
                      WebkitBoxOrient: "vertical",
                      height: "36px", // Fixed height for 2 lines
                      flex: "0 0 auto", // Prevent flex growth
                    }}
                  >
                    {shortDescription || "No description available."}
                  </Typography>
                </Box>
              </Box>
            </SwiperSlide>
          );
        })}
      </Swiper>

      {/* Custom Navigation Arrows */}
      <Box
        sx={{
          display: "flex",
          gap: "8px",
          marginTop: "20px",
          justifyContent: "flex-start",
        }}
      >
        <IconButton
          onClick={() => swiperRef.current?.swiper?.slidePrev()}
          sx={{
            width: "40px",
            height: "40px",
            borderRadius: "4px", // Square shape instead of circle
            backgroundColor: theme.palette.primary.main,
            color: "#fff",
            border: `2px solid ${theme.palette.primary.main}`,
            "&:hover": {
              backgroundColor: "transparent",
              color: theme.palette.primary.main,
              border: `2px solid ${theme.palette.primary.main}`,
            },
          }}
        >
          <ArrowBackIosIcon sx={{ fontSize: "18px", marginLeft: "4px" }} />
        </IconButton>

        <IconButton
          onClick={() => swiperRef.current?.swiper?.slideNext()}
          sx={{
            width: "40px",
            height: "40px",
            borderRadius: "4px", // Square shape instead of circle
            backgroundColor: theme.palette.primary.main,
            color: "#fff",
            border: `2px solid ${theme.palette.primary.main}`,
            "&:hover": {
              backgroundColor: "transparent",
              color: theme.palette.primary.main,
              border: `2px solid ${theme.palette.primary.main}`,
            },
          }}
        >
          <ArrowForwardIosIcon sx={{ fontSize: "18px" }} />
        </IconButton>
      </Box>
    </Box>
  );
};

export default Speciality;
