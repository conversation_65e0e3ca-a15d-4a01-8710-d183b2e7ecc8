"use client";

import {Box} from "@mui/material";
import {useContext,} from "react";
import {AppContext} from "@/app/AppContextLayout";
import WebHighlightsWidgetChainTemp3
    from "@/app/hospitalchaintemplate3/components/highlights/webHighlightsWidgetChainTemp3";
import MWebHighlightsWidgetChainTemp3
    from "@/app/hospitalchaintemplate3/components/highlights/MWebHighlightsWidgetChainTemp3";
import SectionLayoutChainTemp2 from "@/app/hospitalchaintemplate2apollo/styledComponents/SectionLayoutChainTemp2";

const HighlightsWidgetChainTemp3 = () => {
    const {websiteData = {},} = useContext(AppContext);
    const {highlights = []} = websiteData || {};

    return (
        <>
            {highlights.length > 0 && (
                <Box
                    sx={{
                        position: "relative",
                        // width: "max-content",
                        zIndex: 1,
                        display: {xs: "none", md: "block"},
                    }}
                >
                    <WebHighlightsWidgetChainTemp3 highlights={highlights}/>
                </Box>
            )}
            <Box
                sx={{
                    position: "relative",
                    display: {xs: "block", md: "none"},
                }}
            >
                {highlights.length > 0 && (
                        <MWebHighlightsWidgetChainTemp3 highlights={highlights}/>
                    // </SectionLayoutChainTemp2>
                )}
            </Box>
        </>
    );
};

export default HighlightsWidgetChainTemp3;
